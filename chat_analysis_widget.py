import re
from PySide6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                               QLineEdit, QPushButton, QProgressBar, QFileDialog,
                               QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, QThread, Signal
import pandas as pd
import os
from http import HTTPStatus
from dashscope import Application

class AnalysisThread(QThread):
    progress_updated = Signal(int)
    analysis_completed = Signal(str)
    error_occurred = Signal(str)

    def __init__(self, input_file, app_id, api_key):
        super().__init__()
        self.input_file = input_file
        self.app_id = app_id
        self.api_key = api_key

    def run(self):
        try:
            df = pd.read_excel(self.input_file)
            total_rows = len(df)
            df['Score'] = None

            for index, row in df.iterrows():
                customer = row['接待客服']
                session_str = self.parse_conversation(row['会话内容（不包含富文本标签）'], customer)
                score_result = self.qiyuhh(session_str, self.app_id, self.api_key)
                df.at[index, '分析结果'] = score_result

                progress_value = int((index + 1) / total_rows * 100)
                self.progress_updated.emit(progress_value)

            output_file = self.get_output_filename(self.input_file)
            df.to_excel(output_file, index=False)
            self.analysis_completed.emit(output_file)
        except Exception as e:
            self.error_occurred.emit(str(e))

    def parse_conversation(self, conversation_str, customer):
        pattern = r"(\w+)\s+(\d{4}年\d{2}月\d{2}日 \d{2}:\d{2}:\d{2})\s+(.*)"
        matches = re.findall(pattern, conversation_str)

        conversation_list = []
        for match in matches:
            speaker, time, content = match
            formatted_speaker = "customer" if speaker == customer else "user"
            conversation_list.append({
                "speaker": formatted_speaker,
                "time": time,
                "content": content.strip()
            })

        return conversation_list

    def qiyuhh(self, text, app_id, api_key):
        response = Application.call(
            api_key=api_key,
            app_id=app_id,
            prompt=text,)

        if response.status_code != HTTPStatus.OK:
            print(f'request_id={response.request_id}')
            print(f'code={response.status_code}')
            print(f'message={response.message}')
            print(f'请参考文档：https://help.aliyun.com/zh/model-studio/developer-reference/error-code')
            return "分析失败"
        else:
            return response.output.text

    def get_output_filename(self, input_file):
        dir_path = os.path.dirname(input_file)
        file_name = os.path.basename(input_file)
        name, ext = os.path.splitext(file_name)
        return os.path.join(dir_path, f"{name}_分析结果{ext}")

class ChatAnalysisWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.api_key = 'sk-50c890fbbdc24dc59bddc2b8b9f0ee31'  # 固定的API Key
        self.init_ui()
        self.result_table = None

    def init_ui(self):
        layout = QVBoxLayout(self)

        # 输入文件
        file_layout = QHBoxLayout()
        self.input_file_edit = QLineEdit()
        self.input_file_edit.setPlaceholderText("选择输入文件...")
        file_button = QPushButton("浏览")
        file_button.clicked.connect(self.select_input_file)
        file_layout.addWidget(QLabel("输入文件:"))
        file_layout.addWidget(self.input_file_edit)
        file_layout.addWidget(file_button)
        layout.addLayout(file_layout)

        # App ID
        app_id_layout = QHBoxLayout()
        self.app_id_edit = QLineEdit('549606af840443b8904902fadbc90c95')
        app_id_layout.addWidget(QLabel("App ID:"))
        app_id_layout.addWidget(self.app_id_edit)
        layout.addLayout(app_id_layout)

        # 开始分析按钮
        self.start_button = QPushButton("开始分析")
        self.start_button.clicked.connect(self.start_analysis)
        layout.addWidget(self.start_button)

        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel()
        layout.addWidget(self.status_label)

        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(0)
        self.result_table.setRowCount(0)
        self.result_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.result_table.setWordWrap(True)  # 启用自动换行
        self.result_table.verticalHeader().setDefaultSectionSize(60)  # 设置行高以适应换行
        layout.addWidget(self.result_table)

    def select_input_file(self):
        file_name, _ = QFileDialog.getOpenFileName(self, "选择输入文件", "", "Excel Files (*.xlsx *.xls)")
        if file_name:
            self.input_file_edit.setText(file_name)

    def start_analysis(self):
        input_file = self.input_file_edit.text()
        if not input_file:
            self.status_label.setText("请选择输入文件")
            return

        app_id = self.app_id_edit.text()
        self.progress_bar.setValue(0)
        self.status_label.setText("开始分析...")
        self.start_button.setEnabled(False)

        self.analysis_thread = AnalysisThread(input_file, app_id, self.api_key)
        self.analysis_thread.progress_updated.connect(self.update_progress)
        self.analysis_thread.analysis_completed.connect(self.analysis_completed)
        self.analysis_thread.error_occurred.connect(self.analysis_error)
        self.analysis_thread.start()

    def update_progress(self, value):
        self.progress_bar.setValue(value)
        self.status_label.setText(f"处理进度: {value}%")

    def analysis_completed(self, output_file):
        self.status_label.setText("分析完成！")
        self.start_button.setEnabled(True)

        # 读取并显示分析结果
        try:
            df = pd.read_excel(output_file)
            self.display_results(df)
        except Exception as e:
            print(f"读取结果文件时出错: {str(e)}")

        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "完成", f"分析已完成，结果已保存至:\n{output_file}")

    def display_results(self, df):
        # 设置表格列数和标题
        self.result_table.setColumnCount(len(df.columns))
        self.result_table.setHorizontalHeaderLabels(df.columns)

        # 设置数据
        self.result_table.setRowCount(len(df))
        for row in range(len(df)):
            for col in range(len(df.columns)):
                item = QTableWidgetItem(str(df.iloc[row, col]))
                item.setTextAlignment(Qt.AlignTop | Qt.AlignLeft)  # 文本靠上靠左对齐
                self.result_table.setItem(row, col, item)

        # 设置指定列的宽度为80像素并启用自动换行
        content_col = df.columns.get_loc('会话内容（不包含富文本标签）') if '会话内容（不包含富文本标签）' in df.columns else -1
        analysis_col = df.columns.get_loc('分析结果') if '分析结果' in df.columns else -1

        if content_col != -1:
            self.result_table.setColumnWidth(content_col, 80)
        if analysis_col != -1:
            self.result_table.setColumnWidth(analysis_col, 80)

        # 其他列自适应内容宽度
        for col in range(len(df.columns)):
            if col != content_col and col != analysis_col:
                self.result_table.horizontalHeader().setSectionResizeMode(col, QHeaderView.ResizeToContents)

    def analysis_error(self, error_message):
        self.status_label.setText("分析过程中发生错误")
        self.start_button.setEnabled(True)
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.critical(self, "错误", f"处理过程中发生错误: {error_message}")