from PySide6.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout,
                               QPushButton, QStackedWidget, QApplication,
                               QLabel, QFrame, QHBoxLayout)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QIcon, QColor, QPalette
import sys
from chat_analysis_widget import ChatAnalysisWidget
from data_analysis_widget import DataAnalysisWidget

class StyledButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setMinimumHeight(50)
        self.setFont(QFont("Microsoft YaHei", 10))
        self.setCursor(Qt.PointingHandCursor)
        self.setStyleSheet("""
            QPushButton {
                border: none;
                border-radius: 8px;
                background-color: #E5DDD7;
                color: #003153;
                padding: 10px 20px;
                text-align: left;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #d5cdc7;
            }
            QPushButton:pressed {
                background-color: #c5bdb7;
            }
            QPushButton:disabled {
                background-color: #003153;
                color: #E5DDD7;
            }
        """)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("客服数据分析系统")
        self.setMinimumSize(800, 700)
        self.setup_ui()

    def setup_ui(self):
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #003153;
            }
            QFrame {
                background-color: #E5DDD7;
                border-radius: 10px;
            }
            QLabel#title {
                color: #003153;
                padding: 10px;
                font-size: 24px;
                font-weight: bold;
            }
        """)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # 添加标题区域
        title_frame = QFrame()
        title_frame.setMaximumHeight(70)
        title_layout = QHBoxLayout(title_frame)

        title_label = QLabel("客服数据分析系统")
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        main_layout.addWidget(title_frame)

        # 创建内容区域的容器
        content_frame = QFrame()
        content_layout = QHBoxLayout(content_frame)
        content_layout.setContentsMargins(5, 10, 5, 10)
        content_layout.setSpacing(10)

        # 创建左侧导航区域
        nav_frame = QFrame()
        nav_frame.setMaximumWidth(180)
        nav_frame.setStyleSheet("""
            QFrame {
                background-color: #E5DDD7;
                border-radius: 10px;
                padding: 10px;
            }
        """)

        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(5)

        # 添加导航标题
        nav_title = QLabel("功能导航")
        nav_title.setStyleSheet("""
            QLabel {
                color: #003153;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                border-bottom: 2px solid #003153;
            }
        """)
        nav_layout.addWidget(nav_title)

        # 创建导航按钮
        self.btn_chat_analysis = StyledButton("📊 七鱼会话分析")
        self.btn_data_analysis = StyledButton("📈 客服数据分析")

        nav_layout.addWidget(self.btn_chat_analysis)
        nav_layout.addWidget(self.btn_data_analysis)
        nav_layout.addStretch()

        # 创建右侧内容区域
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet("""
            QStackedWidget {
                background-color: #E5DDD7;
                border-radius: 10px;
            }
        """)

        # 创建并添加两个模块窗口部件
        self.chat_analysis = ChatAnalysisWidget()
        self.data_analysis = DataAnalysisWidget()

        self.stacked_widget.addWidget(self.chat_analysis)
        self.stacked_widget.addWidget(self.data_analysis)

        # 将导航和内容添加到内容布局
        content_layout.addWidget(nav_frame)
        content_layout.addWidget(self.stacked_widget, stretch=1)

        # 将内容框架添加到主布局
        main_layout.addWidget(content_frame)

        # 连接按钮点击信号
        self.btn_chat_analysis.clicked.connect(self.show_chat_analysis)
        self.btn_data_analysis.clicked.connect(self.show_data_analysis)

        # 设置状态栏
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #E5DDD7;
                color: #003153;
            }
        """)
        self.statusBar().showMessage("系统就绪")

        # 默认显示对话分析模块
        self.show_chat_analysis()

    def show_chat_analysis(self):
        self.stacked_widget.setCurrentWidget(self.chat_analysis)
        self.btn_chat_analysis.setEnabled(False)
        self.btn_data_analysis.setEnabled(True)
        self.statusBar().showMessage("当前模块：七鱼会话分析")

    def show_data_analysis(self):
        self.stacked_widget.setCurrentWidget(self.data_analysis)
        self.btn_chat_analysis.setEnabled(True)
        self.btn_data_analysis.setEnabled(False)
        self.statusBar().showMessage("当前模块：客服数据分析")

if __name__ == '__main__':
    app = QApplication(sys.argv)

    # 设置应用程序级别的样式
    app.setStyle("Fusion")

    window = MainWindow()
    window.show()
    sys.exit(app.exec())