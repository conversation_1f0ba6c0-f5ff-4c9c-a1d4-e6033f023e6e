from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QLineEdit, QPushButton, QTextEdit, QCheckBox,
                               QFileDialog, QMessageBox, QFrame, QTableWidget,
                               QTableWidgetItem, QTabWidget)
from PySide6.QtCore import Qt, QThread, Signal
import pandas as pd
import json
import os
import requests
import threading

class AnalysisThread(QThread):
    analysis_completed = Signal(str, pd.DataFrame, pd.DataFrame)
    error_occurred = Signal(str)
    log_updated = Signal(str)
    webhook_status = Signal(str)

    def __init__(self, input_file, config, send_webhook=False, webhook_url=""):
        super().__init__()
        self.input_file = input_file
        self.config = config
        self.send_webhook = send_webhook
        self.webhook_url = webhook_url

        # 游戏名到ID的映射
        self.game_id_map = {
            "月圆之夜": "5199",
            "球球大作战": "5078",
            "太空杀": "5256",
            "街篮2": "5218",
            "巨人": "4",
            "仙侠世界": "35",
            "仙侠世界2": "45",
            "帕斯卡契约": "5205",
            "艾尔之光": "25",
            "征途端游": "1",
            "征途2手游": "5174",
            "绿色征途手游": "5234",
            "原始征途手游": "5242",
            "原始征途小程序": "5243",
            "王者征途": "5273",
            "王者征途小程序": "5274",
            "五千年": "5286",
            "征途2": "17",
            "绿色征途": "20",
            "征途2经典专区": "40",
            "征途怀旧": "7",
            "超自然行动组": "5289",
            "口袋斗蛐蛐": "5304"
        }

        # 组名到ID的映射
        self.group_id_map = {
            "国内综合": "group1",
            "征途端游": "group2",
            "原始征途": "group3",
            "王者征途": "group4"
        }

    def get_game_id(self, game_name):
        """根据游戏名获取对应的游戏ID"""
        return self.game_id_map.get(game_name)

    def get_group_id(self, group_name):
        """根据组名获取对应的组ID"""
        return self.group_id_map.get(group_name)

    def run(self):
        try:
            self.log_updated.emit("开始分析...\n")
            input_path = self.input_file
            base_name = os.path.splitext(input_path)[0]
            output_path = f"{base_name}_分析结果.xlsx"

            df = pd.read_excel(input_path, engine='openpyxl')

            # 提取游戏名
            game_names = []
            for entry in df['客户信息上报']:
                try:
                    data = json.loads(entry)
                    game_name = next(
                        (item['value'] for item in data if item.get('label') == '游戏名'),
                        None
                    )
                    game_names.append(game_name)
                except:
                    game_names.append(None)
            df['游戏名'] = game_names

            # 统计游戏总次数
            game_counts = df['游戏名'].value_counts().dropna().to_dict()

            # 统计关键词匹配次数
            keyword_counts = {game: 0 for game in self.config["game_keywords"].keys()}
            for index, row in df.iterrows():
                game = row['游戏名']
                content = str(row['会话内容（不包含富文本标签）']).lower()
                if game in self.config["game_keywords"]:
                    for keyword in self.config["game_keywords"][game]:
                        if keyword.lower() in content:
                            keyword_counts[game] += 1

            # 构建基础统计DataFrame
            base_df = pd.DataFrame({
                "游戏名": list(self.config["game_keywords"].keys()),
                "总次数": [game_counts.get(g, 0) for g in self.config["game_keywords"]],
                "匹配次数": list(keyword_counts.values()),
            })
            base_df["比例(%)"] = (base_df["匹配次数"] / base_df["总次数"]) * 100

            base_df.fillna(0, inplace=True)

            # 处理合并组
            merged_results = {}
            for group_name, group in self.config["merged_groups"].items():
                merged_games = group["games"]
                group_base = base_df[base_df["游戏名"].isin(merged_games)]
                merged_total = group_base["总次数"].sum()
                merged_matched = group_base["匹配次数"].sum()
                merged_ratio = (merged_matched / merged_total) * 100 if merged_total != 0 else 0
                merged_results[group_name] = {
                    "总次数": merged_total,
                    "匹配次数": merged_matched,
                    "比例(%)": merged_ratio
                }

            # 保存结果
            with pd.ExcelWriter(output_path) as writer:
                base_df.to_excel(writer, sheet_name='基础统计', index=False)
                merged_df = pd.DataFrame.from_dict(
                    merged_results,
                    orient='index',
                    columns=["总次数", "匹配次数", "比例(%)"]
                ).reset_index().rename(columns={'index': '组名'})
                merged_df.to_excel(writer, sheet_name='合并组统计', index=False)
                df[['游戏名', '会话内容（不包含富文本标签）']].to_excel(writer, sheet_name='原始数据', index=False)

            # Webhook发送逻辑
            if self.send_webhook and self.webhook_url.strip():
                # 添加请求标记防止重复发送
                if hasattr(self, '_webhook_sent'):
                    self.webhook_status.emit("检测到重复的webhook发送请求，已跳过\n")
                    return

                try:
                    # 标记请求已发送
                    self._webhook_sent = True

                    # 构建游戏数据JSON结构
                    game_data = {}
                    for _, row in base_df.iterrows():
                        game_name = row['游戏名']
                        game_id = self.get_game_id(game_name)
                        if game_id:
                            game_data[game_id] = {
                                "游戏名": game_name,
                                "总次数": int(row['总次数']),  # 转换为Python原生int类型
                                "匹配次数": int(row['匹配次数']),  # 转换为Python原生int类型
                                "比例(%)": float(row['比例(%)'])  # 转换为Python原生float类型
                            }

                    # 构建合并组数据JSON结构
                    merged_data = {}
                    for group_name, group_info in self.config["merged_groups"].items():
                        group_id = self.get_group_id(group_name)
                        if group_id:
                            # 从merged_df获取该组的统计数据
                            group_stats = merged_df[merged_df['组名'] == group_name].iloc[0]
                            merged_data[group_id] = {
                                "游戏名": group_name,
                                "总次数": int(group_stats['总次数']),  # 转换为Python原生int类型
                                "匹配次数": int(group_stats['匹配次数']),  # 转换为Python原生int类型
                                "比例(%)": float(group_stats['比例(%)'])  # 转换为Python原生float类型
                            }

                    # 合并游戏数据和合并组数据
                    json_data = {
                        "data": game_data,
                        "merged_groups": merged_data
                    }

                    # 添加JSON序列化调试日志
                    try:
                        json_str = json.dumps(json_data, ensure_ascii=False, indent=2)
                        self.webhook_status.emit(f"JSON序列化测试成功:\n{json_str[:500]}...\n")
                    except Exception as e:
                        self.webhook_status.emit(f"JSON序列化测试失败: {str(e)}\n")
                        raise

                    response = requests.post(
                        self.webhook_url,
                        json=json_data,
                        headers={'Content-Type': 'application/json'}
                    )
                    self.webhook_status.emit(f"正在发送Webhook请求到: {self.webhook_url}\n")
                    self.webhook_status.emit(f"请求数据: {json.dumps(json_data, indent=2, ensure_ascii=False)}\n")

                    # 记录完整的响应信息
                    log_msg = (
                        f"Webhook响应:\n"
                        f"状态码: {response.status_code}\n"
                        f"响应内容: {response.text}\n"
                        f"响应头: {response.headers}\n"
                    )
                    self.webhook_status.emit(log_msg)

                    if response.status_code == 200:
                        self.webhook_status.emit("Webhook发送成功！\n")
                    else:
                        self.webhook_status.emit(f"Webhook发送失败，状态码：{response.status_code}\n")
                        self.webhook_status.emit(f"详细错误信息: {response.text}\n")
                except Exception as e:
                    error_msg = (
                        f"发送Webhook时出错:\n"
                        f"错误类型: {type(e).__name__}\n"
                        f"错误详情: {str(e)}\n"
                    )
                    self.webhook_status.emit(error_msg)

                    # 如果是requests异常，记录更多连接相关信息
                    if isinstance(e, requests.exceptions.RequestException):
                        self.webhook_status.emit(f"请求异常详情: {vars(e)}\n")
            else:
                self.webhook_status.emit("Webhook未启用或地址未设置，跳过发送。\n")

            self.log_updated.emit(f"分析完成！\n")
            self.log_updated.emit(f"结果已保存到：{output_path}\n")
            self.analysis_completed.emit(output_path, base_df, merged_df)

        except Exception as e:
            self.error_occurred.emit(str(e))

class DataAnalysisWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)

        # 添加选项卡控件
        self.tab_widget = QTabWidget()
        self.input_tab = QWidget()
        self.result_tab = QWidget()
        self.tab_widget.addTab(self.input_tab, "输入")
        self.tab_widget.addTab(self.result_tab, "结果")

        # 设置输入选项卡的布局
        input_layout = QVBoxLayout(self.input_tab)

        # 文件选择区域
        file_frame = QFrame()
        file_frame.setFrameStyle(QFrame.StyledPanel)
        file_layout = QHBoxLayout(file_frame)
        self.input_file_edit = QLineEdit()
        self.input_file_edit.setPlaceholderText("选择输入Excel文件...")
        file_button = QPushButton("浏览")
        file_button.clicked.connect(self.select_input_file)
        file_layout.addWidget(QLabel("输入文件:"))
        file_layout.addWidget(self.input_file_edit)
        file_layout.addWidget(file_button)
        layout.addWidget(file_frame)

        # 配置区域
        config_frame = QFrame()
        config_frame.setFrameStyle(QFrame.StyledPanel)
        config_layout = QVBoxLayout(config_frame)
        config_layout.addWidget(QLabel("配置（JSON格式）:"))
        self.config_edit = QTextEdit()
        self.config_edit.setMinimumHeight(200)
        config_layout.addWidget(self.config_edit)
        layout.addWidget(config_frame)

        # Webhook设置
        webhook_frame = QFrame()
        webhook_frame.setFrameStyle(QFrame.StyledPanel)
        webhook_layout = QHBoxLayout(webhook_frame)
        self.webhook_checkbox = QCheckBox("发送到Webhook")
        self.webhook_url_edit = QLineEdit()
        self.webhook_url_edit.setPlaceholderText("输入Webhook URL...")
        self.webhook_url_edit.setText("https://www.feishu.cn/flow/api/trigger-webhook/0eb73bdeedf5f4309b0e2e00c800ccb1")
        webhook_layout.addWidget(self.webhook_checkbox)
        webhook_layout.addWidget(self.webhook_url_edit)
        layout.addWidget(webhook_frame)

        # 开始按钮
        self.start_button = QPushButton("开始分析")
        self.start_button.clicked.connect(self.start_analysis)
        layout.addWidget(self.start_button)

        # 日志区域
        log_frame = QFrame()
        log_frame.setFrameStyle(QFrame.StyledPanel)
        log_layout = QVBoxLayout(log_frame)
        self.log_edit = QTextEdit()
        self.log_edit.setReadOnly(True)
        log_layout.addWidget(self.log_edit)
        input_layout.addWidget(log_frame)

        # 加载默认配置
        self.load_default_config()

        # 设置结果选项卡的布局
        result_layout = QVBoxLayout(self.result_tab)

        # 添加基础统计结果表格
        self.base_table = QTableWidget()
        result_layout.addWidget(QLabel("基础统计结果:"))
        result_layout.addWidget(self.base_table)

        # 添加合并组统计结果表格
        self.merged_table = QTableWidget()
        result_layout.addWidget(QLabel("合并组统计结果:"))
        result_layout.addWidget(self.merged_table)

        # 将选项卡控件添加到主布局
        layout.addWidget(self.tab_widget)

    def load_default_config(self):
        default_config = """  
        {  
        "game_keywords": {  
        "超自然行动组": [  
        "为您转接您的专属客服"  
        ],
        "球球大作战": [  
        "为您转接您的专属客服"  
        ],  
        "月圆之夜": [  
        "客服为您解答哦"  
        ],  
        "太空杀": [  
        "为您转接您的专属客服"  
        ],  
        "街篮2": [  
        "转接您的专属客服"  
        ],  
        "巨人": [  
        "转接您的专属客服"  
        ],  
        "仙侠世界": [  
        "转接您的专属客服"  
        ],  
        "仙侠世界2": [  
        "转接您的专属客服"  
        ],  
        "帕斯卡契约": [  
        "转接您的专属客服"  
        ],  
        "艾尔之光": [  
        "转接您的专属客服"  
        ],  
        "征途": [  
        "这方面更有经验"  
        ],  
        "征途2": [  
        "这方面更有经验"  
        ],  
        "征途怀旧": [  
        "这方面更有经验"  
        ],  
        "征途2经典专区": [  
        "这方面更有经验"  
        ],  
        "绿色征途": [  
        "这方面更有经验"  
        ],  
        "原始征途手游": [  
        "这方面更有经验"  
        ],  
        "原始征途小程序": [  
        "这方面更有经验"  
        ],  
        "王者征途": [  
        "这方面更有经验"  
        ],  
        "王者征途小程序": [  
        "这方面更有经验"  
        ],  
        "征途2手游": [  
        "这方面更有经验"  
        ],  
        "绿色征途手游": [  
        "这方面更有经验"  
        ],  
        "五千年": [  
        "这方面更有经验"  
        ]  
        },  
        "merged_groups": {  
        "国内综合": {  
        "games": [  
        "街篮2",  
        "巨人",  
        "仙侠世界",  
        "仙侠世界2",  
        "帕斯卡契约",  
        "艾尔之光"  
        ]  
        },  
        "征途端游": {  
        "games": [  
        "征途",  
        "征途2",  
        "征途怀旧",  
        "征途2经典专区",  
        "绿色征途"  
        ]  
        },  
        "原始征途": {  
        "games": [  
        "原始征途手游",  
        "原始征途小程序"  
        ]  
        },  
        "王者征途": {  
        "games": [  
        "王者征途",  
        "王者征途小程序"  
        ]  
        }  
        }  
        }  
        """
        self.config_edit.setPlainText(default_config.strip())

    def select_input_file(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件",
            "",
            "Excel Files (*.xlsx *.xls)"
        )
        if file_name:
            self.input_file_edit.setText(file_name)

    def validate_config(self):
        try:
            config_text = self.config_edit.toPlainText()
            config = json.loads(config_text)
            if "game_keywords" not in config or "merged_groups" not in config:
                raise ValueError("配置缺少 'game_keywords' 或 'merged_groups' 字段")
            return config
        except Exception as e:
            QMessageBox.critical(self, "配置错误", f"配置格式不正确：{str(e)}")
            return None

    def start_analysis(self):
        # 检查是否已有分析在进行中
        if hasattr(self, '_analysis_running') and self._analysis_running:
            QMessageBox.warning(self, "警告", "已有分析正在进行中，请等待完成！")
            return

        input_file = self.input_file_edit.text()
        if not input_file:
            QMessageBox.warning(self, "警告", "请选择输入文件！")
            return

        config = self.validate_config()
        if not config:
            return

        self.log_edit.clear()
        self.start_button.setEnabled(False)
        self._analysis_running = True  # 标记分析开始

        self.analysis_thread = AnalysisThread(
            input_file,
            config,
            self.webhook_checkbox.isChecked(),
            self.webhook_url_edit.text()
        )
        self.analysis_thread.analysis_completed.connect(self.analysis_completed)
        self.analysis_thread.error_occurred.connect(self.analysis_error)
        self.analysis_thread.log_updated.connect(self.update_log)
        self.analysis_thread.webhook_status.connect(self.update_log)
        self.analysis_thread.start()
        self.tab_widget.setCurrentIndex(0)  # 切换到输入选项卡

    def analysis_completed(self, output_path, base_df, merged_df):
        self.start_button.setEnabled(True)
        self._analysis_running = False  # 重置分析状态标记
        QMessageBox.information(self, "完成", f"分析完成！\n结果已保存到：{output_path}")
        self.display_results(base_df, merged_df)
        self.tab_widget.setCurrentIndex(1)  # 切换到结果选项卡

    def display_results(self, base_df, merged_df):
        # 显示基础统计结果
        self.base_table.setColumnCount(len(base_df.columns))
        self.base_table.setRowCount(len(base_df))
        self.base_table.setHorizontalHeaderLabels(base_df.columns)
        for i, row in enumerate(base_df.itertuples()):
            for j, value in enumerate(row[1:]):
                self.base_table.setItem(i, j, QTableWidgetItem(str(value)))
        self.base_table.resizeColumnsToContents()

        # 显示合并组统计结果
        self.merged_table.setColumnCount(len(merged_df.columns))
        self.merged_table.setRowCount(len(merged_df))
        self.merged_table.setHorizontalHeaderLabels(merged_df.columns)
        for i, row in enumerate(merged_df.itertuples()):
            for j, value in enumerate(row[1:]):
                self.merged_table.setItem(i, j, QTableWidgetItem(str(value)))
        self.merged_table.resizeColumnsToContents()

    def analysis_error(self, error_message):
        self.start_button.setEnabled(True)
        self._analysis_running = False  # 异常情况下也重置状态标记
        self.log_edit.append(f"错误：{error_message}")
        QMessageBox.critical(self, "错误", f"分析过程中发生错误：{error_message}")

    def update_log(self, message):
        self.log_edit.append(message)